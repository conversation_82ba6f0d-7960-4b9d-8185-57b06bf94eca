import { 
    Table, 
    TableBody, 
    TableCell, 
    TableContainer, 
    TableHead, 
    TableRow, 
    Paper, 
    Button, 
    Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    fontWeight: 'bold',
    backgroundColor: theme.palette.grey[100],
}));

const ErplyHeaderCell = styled(TableCell)(({ theme }) => ({
    fontWeight: 'bold',
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    textAlign: 'center',
}));

const ShopifyHeaderCell = styled(TableCell)(({ theme }) => ({
    fontWeight: 'bold',
    backgroundColor: theme.palette.secondary.main,
    color: theme.palette.secondary.contrastText,
    textAlign: 'center',
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
    '&:nth-of-type(odd)': {
        backgroundColor: theme.palette.action.hover,
    },
    '&:hover': {
        backgroundColor: theme.palette.action.selected,
    },
}));

const VariantsTable = ({ variants, handleMapping }) => {
    return (
        <Paper sx={{ width: "100%", overflow: "hidden", marginBottom: "20px" }}>
            <TableContainer sx={{ maxHeight: 600 }}>
                <Table stickyHeader aria-label="variants table" sx={{ minWidth: 1200 }}>
                    <TableHead>
                        <TableRow>
                            <ErplyHeaderCell colSpan={6}>ERPLY VARIANT DETAILS</ErplyHeaderCell>
                            <ShopifyHeaderCell colSpan={5}>SHOPIFY VARIANT DETAILS</ShopifyHeaderCell>
                            <StyledTableCell>Actions</StyledTableCell>
                        </TableRow>
                        <TableRow>
                            {/* Erply columns */}
                            <StyledTableCell sx={{ minWidth: '100px' }}>Product ID</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '150px' }}>Product Code</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '200px' }}>Product Name</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Barcode</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Price (VAT)</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Status</StyledTableCell>
                            
                            {/* Shopify columns */}
                            <StyledTableCell sx={{ minWidth: '200px' }}>Display Name</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '150px' }}>SKU</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Barcode</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Price</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Status</StyledTableCell>
                            
                            {/* Actions */}
                            <StyledTableCell sx={{ minWidth: '150px' }}>Actions</StyledTableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {variants?.map((variant, index) => (
                            <StyledTableRow key={variant.productID || index}>
                                {/* Erply data */}
                                <TableCell>{variant.productID || "-"}</TableCell>
                                <TableCell>{variant.code || "-"}</TableCell>
                                <TableCell 
                                    sx={{ 
                                        maxWidth: '200px', 
                                        overflow: 'hidden', 
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap'
                                    }}
                                    title={variant.name}
                                >
                                    {variant.name || "-"}
                                </TableCell>
                                <TableCell>{variant.code2 || "-"}</TableCell>
                                <TableCell>${Number(variant.priceWithVat || 0).toFixed(2)}</TableCell>
                                <TableCell>
                                    {variant.status || "-"}
                                </TableCell>
                                
                                {/* Shopify data */}
                                <TableCell 
                                    sx={{ 
                                        maxWidth: '200px', 
                                        overflow: 'hidden', 
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap'
                                    }}
                                    title={variant.shopify_product?.displayName}
                                >
                                    {variant.shopify_product?.displayName || (
                                        <Typography variant="body2" color="textSecondary" sx={{ fontStyle: 'italic' }}>
                                            No mapping
                                        </Typography>
                                    )}
                                </TableCell>
                                <TableCell>{variant.shopify_product?.sku || "-"}</TableCell>
                                    <TableCell>{variant.shopify_product?.barcode || "-"}</TableCell>
                                <TableCell>
                                    {variant.shopify_product?.price ? 
                                        `$${Number(variant.shopify_product.price).toFixed(2)}` : "-"
                                    }
                                </TableCell>
                                <TableCell>
                                    {variant.shopify_product?.status || "-"}
                                </TableCell>
                                
                                {/* Actions */}
                                <TableCell>
                                    <Button 
                                        variant="contained" 
                                        size="small"
                                        onClick={() => handleMapping(variant)}
                                    >
                                        {variant.shopify_product ? 'Re-map' : 'Map'}
                                    </Button>
                                </TableCell>
                            </StyledTableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </Paper>
    );
};

export default VariantsTable;




